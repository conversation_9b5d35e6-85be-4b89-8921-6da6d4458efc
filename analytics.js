// Google Analytics 4 Configuration
// Replace 'GA_MEASUREMENT_ID' with your actual Google Analytics 4 Measurement ID

// Google Analytics 4 (GA4) - Modern tracking
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

// Configure your GA4 Measurement ID here
// gtag('config', 'GA_MEASUREMENT_ID');

// Custom events for lawyer website
function trackConsultationRequest() {
    gtag('event', 'consultation_request', {
        'event_category': 'engagement',
        'event_label': 'consultation_form'
    });
}

function trackPhoneCall(phoneType) {
    gtag('event', 'phone_call', {
        'event_category': 'contact',
        'event_label': phoneType,
        'value': 1
    });
}

function trackWhatsAppClick() {
    gtag('event', 'whatsapp_click', {
        'event_category': 'contact',
        'event_label': 'whatsapp_link'
    });
}

function trackServiceView(serviceName) {
    gtag('event', 'service_view', {
        'event_category': 'services',
        'event_label': serviceName
    });
}

// Track scroll depth
let maxScroll = 0;
window.addEventListener('scroll', function() {
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
        maxScroll = scrollPercent;
        gtag('event', 'scroll', {
            'event_category': 'engagement',
            'event_label': scrollPercent + '%'
        });
    }
});

// Instructions for setup:
// 1. Create Google Analytics 4 account at https://analytics.google.com
// 2. Get your Measurement ID (format: G-XXXXXXXXXX)
// 3. Replace 'GA_MEASUREMENT_ID' above with your actual ID
// 4. Add this script to your HTML head section

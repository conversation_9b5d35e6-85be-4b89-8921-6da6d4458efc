/*
    موقع المحامي الأستاذ سامر حمدو - ملف التصميم
    تطوير وتصميم: المبرمج حمدو حمدو
    جميع الحقوق محفوظة © 2025

    Developer: Hamdo Hamdo
    Website: Lawyer Samer <PERSON>do Professional Website
    All Rights Reserved © 2025
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-brown: #2d1a04;
    --secondary-brown: #3d2408;
    --dark-brown: #1a0f02;
    --gold: #b8941f;
    --dark-gold: #8b6914;
    --light-gold: #d4af37;
    --black: #0d0d0d;
    --dark-gray: #1a1a1a;
    --medium-gray: #2c2c2c;
    --light-gray: #e8e8e8;
    --white: #ffffff;
    --text-dark: #1a1a1a;
    --text-light: #4a4a4a;
    --accent-dark: #0f0f0f;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    direction: rtl;
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles - Dark Classic */
.header {
    background: linear-gradient(135deg, var(--dark-brown) 0%, var(--primary-brown) 100%) !important;
    color: var(--white);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.5);
    border-bottom: 2px solid var(--dark-gold);
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-brand .logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--gold);
    padding: 5px;
    background: var(--white);
}

.nav-brand .brand-text h2 {
    color: var(--light-gold) !important;
    margin-bottom: 0.2rem;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.nav-brand .brand-text p {
    font-size: 0.9rem;
    color: var(--gold) !important;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-menu a {
    color: var(--light-gray) !important;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.nav-menu a:hover {
    color: var(--light-gold) !important;
    background: rgba(184, 148, 31, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gold);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-menu a:hover::after {
    width: 80%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 4px;
}

.hamburger span {
    width: 24px;
    height: 2.5px;
    background: var(--gold);
    margin: 2.5px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -5px);
}

/* Hero Section */
.hero {
    background: url('images/background.jfif') center/cover no-repeat;
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 15, 2, 0.95) 0%, rgba(13, 13, 13, 0.1) 100%);
    backdrop-filter: blur(5px);
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
    max-width: 900px;
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--gold);
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.4rem;
    margin-bottom: 2.5rem;
    color: var(--light-gold);
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1rem;
}

.btn {
    padding: 15px 35px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--dark-gold) 0%, var(--accent-dark) 100%);
    color: var(--light-gold);
    border: 2px solid var(--dark-gold);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--gold) 0%, var(--dark-gold) 100%);
    color: var(--black);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.btn-secondary {
    background: rgba(0, 0, 0, 0.3);
    color: var(--light-gold);
    border: 2px solid var(--dark-gold);
    backdrop-filter: blur(15px);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.btn-secondary:hover {
    background: var(--dark-gold);
    color: var(--black);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}



/* Section Styles */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--gold) 0%, var(--dark-gold) 100%);
    border-radius: 2px;
}

.section-header h2 {
    font-size: 3rem;
    color: var(--light-gold);
    margin-bottom: 1.5rem;
    font-weight: 700;
    position: relative;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.8;
}

/* About Section */
.about {
    background: linear-gradient(135deg, var(--dark-gray) 0%, var(--medium-gray) 100%);
    color: var(--light-gray);
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--dark-brown) 0%, var(--dark-gold) 50%, var(--dark-brown) 100%);
}

.about-content {
    max-width: 900px;
    margin: 0 auto;
}

.about-text h3 {
    font-size: 2.5rem;
    color: var(--light-gold);
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.lawyer-bio {
    font-size: 1.2rem;
    line-height: 1.9;
    margin-bottom: 3rem;
    color: var(--light-gray);
    text-align: justify;
}

.qualifications {
    display: grid;
    gap: 2.5rem;
    margin-top: 2rem;
}

.qualification {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(82, 49, 7, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.qualification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(180deg, var(--gold) 0%, var(--primary-brown) 100%);
}

.qualification:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(82, 49, 7, 0.15);
}

.qualification i {
    font-size: 2.5rem;
    color: var(--gold);
    margin-top: 0.5rem;
    padding: 15px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 50%;
    border: 2px solid rgba(212, 175, 55, 0.3);
}

.qualification h4 {
    color: var(--primary-brown);
    margin-bottom: 0.8rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.qualification p {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 1.1rem;
}

/* Services Section */
.services {
    background: linear-gradient(135deg, var(--black) 0%, var(--dark-gray) 100%);
    color: var(--light-gray);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--dark-brown) 0%, var(--dark-gold) 50%, var(--dark-brown) 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

.service-card {
    background: linear-gradient(135deg, var(--accent-dark) 0%, var(--black) 100%);
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    border: 2px solid var(--dark-gold);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(184, 148, 31, 0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.4s ease;
}

.service-card:hover::before {
    transform: scale(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
    border-color: var(--light-gold);
}

.service-card i {
    font-size: 4rem;
    color: var(--light-gold);
    margin-bottom: 1.5rem;
    padding: 20px;
    background: rgba(184, 148, 31, 0.2);
    border-radius: 50%;
    border: 3px solid var(--dark-gold);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.service-card:hover i {
    background: var(--dark-gold);
    color: var(--black);
    transform: scale(1.1);
    border-color: var(--light-gold);
}

.service-card h3 {
    color: var(--light-gold);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.service-card p {
    color: var(--light-gray);
    line-height: 1.8;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

/* Contact Section */
.contact {
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--secondary-brown) 100%);
    color: var(--white);
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--gold) 0%, var(--light-gold) 50%, var(--gold) 100%);
}

.contact .section-header h2 {
    color: var(--gold);
}

.contact .section-header p {
    color: var(--light-gold);
}

.contact-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    max-width: 600px;
    width: 100%;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    border: 1px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--gold);
}

.contact-item i {
    font-size: 2rem;
    color: var(--gold);
    margin-top: 0.5rem;
    padding: 15px;
    background: rgba(212, 175, 55, 0.2);
    border-radius: 50%;
    border: 2px solid rgba(212, 175, 55, 0.4);
}

.contact-item h4 {
    color: var(--light-gold);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.contact-item p {
    color: var(--white);
    font-size: 1.2rem;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    direction: ltr;
    text-align: left;
    margin-bottom: 0.5rem;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border-left: 3px solid var(--gold);
    font-family: 'Courier New', monospace;
}

.whatsapp-link {
    color: #25d366;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.whatsapp-link:hover {
    color: #20b358;
    text-decoration: underline;
}

.whatsapp-link:hover {
    text-decoration: underline;
}

/* Form Styles */
.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 1px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(15px);
}

.form-group {
    margin-bottom: 2rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--gold);
    background: var(--white);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 140px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-light);
    font-weight: 500;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--black) 0%, var(--primary-brown) 100%);
    color: var(--white);
    padding: 4rem 0 1rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--gold) 0%, var(--light-gold) 50%, var(--gold) 100%);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--gold);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
    position: relative;
}

.footer-section h3::after,
.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gold);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section a {
    color: var(--light-gold);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-section a:hover {
    color: var(--gold);
    padding-right: 5px;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--gold) 0%, var(--dark-gold) 100%);
    color: var(--black);
    border-radius: 50%;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    border: 2px solid var(--gold);
}

.social-links a:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, var(--dark-gold) 0%, var(--gold) 100%);
}

.footer-bottom {
    text-align: center;
    padding-top: 2.5rem;
    border-top: 2px solid rgba(212, 175, 55, 0.3);
    color: var(--light-gold);
    font-size: 1.1rem;
    font-weight: 500;
}

.footer-bottom p {
    margin-bottom: 0.5rem;
}

.developer-credit {
    font-size: 0.9rem !important;
    color: rgba(212, 175, 55, 0.8) !important;
    font-weight: 400 !important;
    margin-top: 0.5rem;
}

.developer-credit strong {
    color: var(--gold);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: linear-gradient(135deg, var(--dark-brown) 0%, var(--primary-brown) 100%);
        flex-direction: column;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        z-index: 999;
        padding: 1.2rem 0;
    }

    .nav-menu.active {
        display: flex !important;
        animation: slideDown 0.3s ease;
    }

    .nav-menu li {
        margin: 0.6rem 0;
    }

    .nav-menu a {
        font-size: 1.1rem;
        padding: 0.8rem 1.5rem;
        display: block;
        border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    }

    .nav-menu a:hover {
        background: rgba(212, 175, 55, 0.2);
        transform: none;
    }

    .hamburger {
        display: flex;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .nav-brand {
        gap: 0.5rem;
    }

    .nav-brand .logo {
        width: 40px;
        height: 40px;
    }

    .nav-brand .brand-text h2 {
        font-size: 1.2rem;
    }

    .hero .container {
        text-align: center;
        max-width: 90%;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1rem;
        line-height: 1.6;
    }

    .contact-content {
        flex-direction: column;
        gap: 2rem;
    }

    .contact-info {
        gap: 1.5rem;
    }

    .contact-item {
        padding: 1.2rem;
        gap: 1rem;
    }

    .contact-item i {
        font-size: 1.5rem;
        padding: 10px;
        margin-top: 0.2rem;
    }

    .contact-item h4 {
        font-size: 1.1rem;
        margin-bottom: 0.6rem;
    }

    .contact-item p {
        font-size: 1rem;
        padding: 6px 8px;
        margin-bottom: 0.3rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .contact-form h3 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
    }

    .form-group input,
    .form-group textarea {
        font-size: 0.9rem;
        padding: 0.8rem;
    }

    .form-group textarea {
        min-height: 100px;
    }

    .whatsapp-link {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}
